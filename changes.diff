diff --git a/models/composite/oDrawManagement/BorrowerDrawCategory.php b/models/composite/oDrawManagement/BorrowerDrawCategory.php
index 108ce888dc..7543a60a4e 100644
--- a/models/composite/oDrawManagement/BorrowerDrawCategory.php
+++ b/models/composite/oDrawManagement/BorrowerDrawCategory.php
@@ -2,12 +2,13 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
+use models\composite\oDrawManagement\traits\PropertiesMapper;
 use models\lendingwise\tblDrawRequestCategories;
 use models\lendingwise\tblDrawRequestLineItems;
 use models\lendingwise\db\tblDrawRequestLineItems_db;

 class BorrowerDrawCategory extends strongType
 {
+    use PropertiesMapper;
     /**
      * @var int|null The ID of the category.
      */
@@ -51,6 +52,7 @@
      */
     public function __construct(?tblDrawRequestCategories $category = null) {
         if ($category == null) $category = new tblDrawRequestCategories();
+        $this->category = $category;
         $this->setProperties($category);
     }

@@ -165,13 +167,8 @@
      * @return void
      */
     private function setProperties(tblDrawRequestCategories $category): void {
-        $this->id = $category->id;
-        $this->drawId = $category->drawId;
-        $this->categoryName = $category->categoryName;
-        $this->description = $category->description;
-        $this->order = $category->order;
-        $this->createdAt = $category->createdAt;
-        $this->updatedAt = $category->updatedAt;
-        $this->category = $category;
+        $this->setPropertiesFromObject($category);
+        // Ensure the DB object is explicitly set after mapping
+        $this->category = $category;
     }
 }
diff --git a/models/composite/oDrawManagement/BorrowerDrawLineItem.php b/models/composite/oDrawManagement/BorrowerDrawLineItem.php
index 8d848b844a..28b2eb5e2c 100644
--- a/models/composite/oDrawManagement/BorrowerDrawLineItem.php
+++ b/models/composite/oDrawManagement/BorrowerDrawLineItem.php
@@ -2,11 +2,12 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
+use models\composite\oDrawManagement\traits\PropertiesMapper;
 use models\lendingwise\tblDrawRequestLineItems;
-use models\composite\oDrawManagement\DrawRequest;

 class BorrowerDrawLineItem extends strongType
 {
+    use PropertiesMapper;
     /**
      * @var int|null The ID of the line item.
      */
@@ -52,7 +53,7 @@
     /**
      * @var float The disbursed amount of the line item.
      */
-    public ?float $disbursedAmount = 0.00;
+    public ?float $amountApproved = 0.00;

     /**
      * @var string|null Notes for the line item.
@@ -89,6 +90,7 @@
      */
     public function __construct(?tblDrawRequestLineItems $lineItem = null) {
         if ($lineItem == null) $lineItem = new tblDrawRequestLineItems();
+        $this->lineItem = $lineItem;
         $this->setProperties($lineItem);
     }

@@ -116,7 +118,7 @@
         $this->lineItem->completedAmount = $lineItemData['completedAmount'] ?? $this->lineItem->completedAmount;
         $this->lineItem->completedPercent = $lineItemData['completedPercent'] ?? $this->lineItem->completedPercent;
         $this->lineItem->requestedAmount = $lineItemData['requestedAmount'] ?? $this->lineItem->requestedAmount;
-        $this->lineItem->disbursedAmount = $lineItemData['disbursedAmount'] ?? $this->lineItem->disbursedAmount;
+        $this->lineItem->amountApproved = $lineItemData['amountApproved'] ?? $this->lineItem->amountApproved;
         $this->lineItem->notes = $lineItemData['notes'] ?? $this->lineItem->notes;
         $this->lineItem->lenderNotes = $lineItemData['lenderNotes'] ?? $this->lineItem->lenderNotes;
         $this->lineItem->rejectReason = $lineItemData['rejectReason'] ?? $this->lineItem->rejectReason;
@@ -151,7 +153,7 @@
             "cost" => $this->cost,
             "completedAmount" => $this->completedAmount,
             "completedPercent" => $this->completedPercent,
-            "requestedAmount" => $this->requestedAmount,
-            "disbursedAmount" => $this->disbursedAmount,
+            "requestedAmount" => $this->requestedAmount,
+            "amountApproved" => $this->amountApproved,
             "notes" => $this->notes,
             "lenderNotes" => $this->lenderNotes,
             "rejectReason" => $this->rejectReason
@@ -166,22 +168,17 @@
      * @return void
      */
     private function setProperties(tblDrawRequestLineItems $lineItem): void {
-        $this->id = $lineItem->id;
-        $this->drawId = $lineItem->drawId;
-        $this->categoryId = $lineItem->categoryId;
-        $this->name = $lineItem->name;
+        $this->setPropertiesFromObject($lineItem);
+
+        // Handle potential null values with defaults after mapping
         $this->description = $lineItem->description ?? '';
-        $this->order = $lineItem->order;
-        $this->cost = $lineItem->cost;
         $this->completedAmount = $lineItem->completedAmount ?? 0.00;
         $this->completedPercent = $lineItem->completedPercent ?? 0.00;
         $this->requestedAmount = $lineItem->requestedAmount ?? 0.00;
-        $this->disbursedAmount = $lineItem->disbursedAmount ?? 0.00;
+        $this->amountApproved = $lineItem->amountApproved ?? 0.00;
         $this->notes = $lineItem->notes ?? '';
         $this->lenderNotes = $lineItem->lenderNotes ?? '';
         $this->rejectReason = $lineItem->rejectReason ?? '';
-        $this->createdAt = $lineItem->createdAt;
-        $this->updatedAt = $lineItem->updatedAt;
-        $this->lineItem = $lineItem;
+        // Ensure the DB object is explicitly set
+        $this->lineItem = $lineItem;
     }
 }
diff --git a/models/composite/oDrawManagement/BorrowerDrawLineItemHistory.php b/models/composite/oDrawManagement/BorrowerDrawLineItemHistory.php
index c605b7aa06..7e6f8887b4 100644
--- a/models/composite/oDrawManagement/BorrowerDrawLineItemHistory.php
+++ b/models/composite/oDrawManagement/BorrowerDrawLineItemHistory.php
@@ -2,10 +2,12 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
+use models\composite\oDrawManagement\traits\PropertiesMapper;
 use models\lendingwise\tblDrawRequestLineItems_h;

 class BorrowerDrawLineItemHistory extends strongType
 {
+    use PropertiesMapper;
     /**
      * @var int|null The ID of the history record.
      */
@@ -70,6 +72,7 @@
      */
     public function __construct(?tblDrawRequestLineItems_h $lineItemHistory = null) {
         if ($lineItemHistory == null) $lineItemHistory = new tblDrawRequestLineItems_h();
+        $this->lineItemHistory = $lineItemHistory;
         $this->setProperties($lineItemHistory);
     }

@@ -90,16 +93,8 @@
      * @return void
      */
     private function setProperties(tblDrawRequestLineItems_h $lineItemHistory): void {
-        $this->lineItemHistory = $lineItemHistory;
-        $this->id = $lineItemHistory->id;
-        $this->recordId = $lineItemHistory->recordId;
-        $this->lineItemId = $lineItemHistory->lineItemId;
-        $this->completedAmount = $lineItemHistory->completedAmount;
-        $this->completedPercent = $lineItemHistory->completedPercent;
-        $this->requestedAmount = $lineItemHistory->requestedAmount;
-        $this->disbursedAmount = $lineItemHistory->disbursedAmount;
-        $this->notes = $lineItemHistory->notes;
-        $this->lenderNotes = $lineItemHistory->lenderNotes;
-        $this->createdAt = $lineItemHistory->createdAt;
+        $this->setPropertiesFromObject($lineItemHistory);
+        // Ensure the DB object is explicitly set
+        $this->lineItemHistory = $lineItemHistory;
     }

     /**
diff --git a/models/composite/oDrawManagement/DrawRequest.php b/models/composite/oDrawManagement/DrawRequest.php
index 89b1f00ba5..f9479e09d0 100644
--- a/models/composite/oDrawManagement/DrawRequest.php
+++ b/models/composite/oDrawManagement/DrawRequest.php
@@ -2,14 +2,14 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
+use models\composite\oDrawManagement\traits\PropertiesMapper;
 use models\lendingwise\tblDrawRequests;
 use models\lendingwise\db\tblDrawRequestCategories_db;
 use models\lendingwise\tblDrawRequestCategories;
-use models\composite\oDrawManagement\BorrowerDrawCategory;
-use models\composite\oDrawManagement\BorrowerDrawLineItem;
-use models\standard\Dates;

 class DrawRequest extends strongType
 {
+    use PropertiesMapper;
     /**
      * Status constants
      */
@@ -63,6 +63,7 @@
      */
     public function __construct(?tblDrawRequests $drawRequest = null) {
         if ($drawRequest == null) $drawRequest = new tblDrawRequests();
+        $this->drawRequest = $drawRequest;
         $this->setProperties($drawRequest);
         if ($drawRequest->id) $this->loadCategories();
     }
@@ -135,13 +136,8 @@
      * @return void
      */
     private function setProperties(tblDrawRequests $drawRequest): void {
-        $this->id = $drawRequest->id;
-        $this->LMRId = $drawRequest->LMRId;
-        $this->status = $drawRequest->status;
-        $this->sowApproved = $drawRequest->sowApproved;
-        $this->isDrawRequest = $drawRequest->isDrawRequest;
-        $this->updatedAt = $drawRequest->updatedAt;
-        $this->drawRequest = $drawRequest;
+        $this->setPropertiesFromObject($drawRequest);
+        $this->drawRequest = $drawRequest; // Ensure the DB object is explicitly set
     }

     /**
diff --git a/models/composite/oDrawManagement/DrawRequestManager.php b/models/composite/oDrawManagement/DrawRequestManager.php
index 168b929840..176a39b265 100644
--- a/models/composite/oDrawManagement/DrawRequestManager.php
+++ b/models/composite/oDrawManagement/DrawRequestManager.php
@@ -2,22 +2,18 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
-use models\cypher;
 use models\lendingwise\tblFile;
 use models\lendingwise\tblDrawRequests;
 use models\lendingwise\db\tblDrawRequests_db;
 use models\lendingwise\tblDrawRequests_h;
 use models\lendingwise\tblDrawRequestLineItems_h;
 use models\lendingwise\db\tblDrawRequestLineItems_h_db;
-use models\composite\oDrawManagement\DrawRequest;
-use models\composite\oDrawManagement\BorrowerDrawCategory;
-use models\composite\oDrawManagement\BorrowerDrawLineItem;
-use models\composite\oDrawManagement\SowTemplateManager;
-use models\composite\oDrawManagement\DrawRequestsHistory;
+use models\composite\oDrawManagement\traits\DrawRequestHistoryHandler;
+use models\composite\oDrawManagement\traits\DrawRequestValidation;
 use models\Database2;

-class DrawRequestManager extends strongType
-{
+class DrawRequestManager extends strongType {
+    use DrawRequestValidation;
+    use DrawRequestHistoryHandler;
     /**
      * @var int|null The LMR ID associated with the draw request manager.
      */
@@ -512,17 +508,18 @@
         try {
             $status = $postData['status'];
             $sowApproved = $this->drawRequest->sowApproved;
-            $isDrawRequest = $this->drawRequest->sowApproved;
-            $previousStatus = $this->drawRequest->status;
+            $isDrawRequest = $this->drawRequest->isDrawRequest;

             if ($status === DrawRequest::STATUS_APPROVED) {
                 $sowApproved = 1;
+                $isDrawRequest = 1; // Once SOW is approved, it becomes a draw request flow
             }

             $this->drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);

             if (isset($postData['lineItems']) && is_array($postData['lineItems'])) {
                 foreach ($postData['lineItems'] as $lineItemId => $lineItemData) {
-                    $lineItem = $this->getLineItemById((int)$lineItemId);
+                    $lineItem = $this->getLineItemById((int) $lineItemId);
                     // Validate line item data before processing
                     $this->validateLineItemData($lineItem, $lineItemData, $status);

@@ -530,7 +527,7 @@
                     $lineItemDbObj->completedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
                     $lineItemDbObj->completedPercent = $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status);
                     $lineItemDbObj->requestedAmount = $this->getLineItemRequestedAmount($lineItem, $lineItemData, $status);
-                    $lineItemDbObj->disbursedAmount = $this->getLineItemDisbursedAmount($lineItem, $lineItemData, $status);
+                    $lineItemDbObj->amountApproved = ($status === DrawRequest::STATUS_APPROVED) ? ($lineItemData['requestedAmount'] ?? 0) : 0;
                     $lineItemDbObj->notes = !empty($lineItemData['notes']) ? $lineItemData['notes'] : $lineItemDbObj->notes;
                     $lineItemDbObj->lenderNotes = !empty($lineItemData['lenderNotes']) ? $lineItemData['lenderNotes'] : $lineItemDbObj->lenderNotes;
                     $lineItemDbObj->rejectReason = !empty($lineItemData['rejectReason']) ? $lineItemData['rejectReason'] : $lineItemDbObj->rejectReason;
@@ -547,78 +544,9 @@
             return true;
         } catch (\Exception $e) {
             $db->rollBack();
+            error_log("DrawRequestManager save failed: " . $e->getMessage());
             return false;
         }
-    }
-
-    /**
-     * Handle draw request history creation and updates.
-     * @param string $status The new status.
-     * @param array $postData The post data containing line items.
-     * @return void
-     */
-    private function handleDrawRequestHistory(string $status, array $lineItemsData): void
-    {
-        $previousStatus = $this->drawRequest->status;
-
-        if ($status === DrawRequest::STATUS_PENDING && $previousStatus !== DrawRequest::STATUS_REJECTED) {
-            DrawRequestsHistory::createHistoryRecord($this->drawRequest->id, $status, $lineItemsData);
-        }
-        else {
-            $preparedLineItemsData = $this->prepareLineItemsDataForHistory($lineItemsData, $status);
-            DrawRequestsHistory::updateLatestHistoryRecord($this->drawRequest->id, $status, $preparedLineItemsData);
-        }
-    }
-
-    /**
-     * Prepare line items data for history creation.
-     * @param array $lineItemsData The raw line items data from post.
-     * @return array Prepared line items data with calculated values.
-     */
-    private function prepareLineItemsDataForHistory(array $lineItemsData, $status): array
-    {
-        $preparedData = [];
-
-        foreach ($lineItemsData as $lineItemId => $lineItemData) {
-            $lineItem = $this->getLineItemById((int)$lineItemId);
-            if (!$lineItem) {
-                continue;
-            }
-
-            $preparedData[$lineItemId] = [
-                'completedAmount' => $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status),
-                'completedPercent' => $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status),
-                'requestedAmount' => $this->getLineItemRequestedAmount($lineItem, $lineItemData, $status),
-                'approvedAmount' => $this->getLineItemApprovedAmount($lineItemData, $status),
-                'disbursedAmount' => $this->getLineItemDisbursedAmount($lineItem, $lineItemData, $status),
-                'notes' => $lineItemData['notes'] ?? null,
-                'lenderNotes' => $lineItemData['lenderNotes'] ?? null
-            ];
-        }
-
-        return $preparedData;
     }

     /**
@@ -754,9 +682,9 @@
     private function getLineItemDisbursedAmount(BorrowerDrawLineItem $lineItem, $lineItemData, $status): float
     {
         if($this->drawRequest->sowApproved && $status === DrawRequest::STATUS_APPROVED) {
-            $disbursedAmount = $lineItem->disbursedAmount + $lineItemData['requestedAmount'];
+            $disbursedAmount = $lineItem->amountApproved + $lineItemData['requestedAmount'];
         } else {
-            $disbursedAmount = $lineItem->disbursedAmount;
+            $disbursedAmount = $lineItem->amountApproved;
         }
         return $disbursedAmount;
     }
@@ -794,116 +722,4 @@
         $fileData = tblFile::Get(['LMRId' => $LMRId]);
         return $fileData ? $fileData->FPCID : null;
     }
-
-    /**
-     * Validate line item data for pending or approved status.
-     * @param BorrowerDrawLineItem $lineItem The line item object.
-     * @param array $lineItemData The line item data to validate.
-     * @param string $status The status of the draw request.
-     * @return void
-     * @throws \InvalidArgumentException If validation fails.
-     */
-    private function validateLineItemData(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): void
-    {
-        // Only validate for pending or approved status
-        if (!in_array($status, [DrawRequest::STATUS_PENDING, DrawRequest::STATUS_APPROVED])) {
-            return;
-        }
-
-        if (!$lineItem instanceof BorrowerDrawLineItem) {
-            throw new \InvalidArgumentException("Line item cannot be null for validation");
-        }
-
-        $cost = (float)($lineItemData['cost'] ?? $lineItem->cost);
-        $completedAmount = (float)$lineItem->completedAmount;
-        $requestedAmount = isset($lineItemData['requestedAmount']) ? (float)$lineItemData['requestedAmount'] : 0;
-        $disbursedAmount = (float)$lineItem->disbursedAmount;
-
-        $newCompletedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
-        $newCompletedPercent = $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status);
-        $newDisbursedAmount = $status === DrawRequest::STATUS_APPROVED
-            ? $disbursedAmount + $requestedAmount
-            : $disbursedAmount;
-
-        // Rule 1: None of these can be negative
-        $this->validateNonNegativeValues($cost, $completedAmount, $requestedAmount, $disbursedAmount, $newCompletedAmount, $newDisbursedAmount);
-
-        // Rule 2: requestedAmount and disbursedAmount don't exceed cost or available amount
-        $this->validateAmountsNotExceedingCost($cost, $requestedAmount, $newDisbursedAmount, $newCompletedAmount);
-
-        // Rule 3: completedPercentage can't be more than 100%
-        $this->validateCompletedPercentage($newCompletedPercent, $newCompletedAmount, $cost);
-    }
-
-    /**
-     * Validate that all amounts are non-negative.
-     * @param float ...$amounts Variable number of amounts to validate.
-     * @return void
-     * @throws \InvalidArgumentException If any amount is negative.
-     */
-    private function validateNonNegativeValues(float ...$amounts): void
-    {
-        $fieldNames = ['cost', 'completedAmount', 'requestedAmount', 'disbursedAmount', 'newCompletedAmount', 'newDisbursedAmount'];
-
-        foreach ($amounts as $index => $amount) {
-            if ($amount < 0) {
-                $fieldName = $fieldNames[$index] ?? 'amount';
-                throw new \InvalidArgumentException("Line item {$fieldName} cannot be negative. Value: {$amount}");
-            }
-        }
-    }
-
-    /**
-     * Validate that requested and disbursed amounts don't exceed cost. Requested amount can't exceed cost minus completed amount.
-     * @param float $cost The total cost of the line item.
-     * @param float $requestedAmount The requested amount.
-     * @param float $disbursedAmount The disbursed amount.
-     * @return void
-     * @throws \InvalidArgumentException If amounts exceed cost.
-     */
-    private function validateAmountsNotExceedingCost(float $cost, float $requestedAmount, float $disbursedAmount, float $completedAmount): void
-    {
-        $availableAmount = $cost - $completedAmount;
-
-        if ($requestedAmount > $availableAmount) {
-            throw new \InvalidArgumentException(
-                "Requested amount ({$requestedAmount}) cannot exceed available amount ({$availableAmount}). " .
-                "Cost: {$cost}, Completed: {$completedAmount}"
-            );
-        }
-
-        if ($disbursedAmount > $cost) {
-            throw new \InvalidArgumentException("Disbursed amount ({$disbursedAmount}) cannot exceed line item cost ({$cost})");
-        }
-    }
-
-    /**
-     * Validate that completed percentage doesn't exceed 100% or is not calculated correctly.
-     * @param float $completedPercent The completed percentage.
-     * @param float $completedAmount The completed amount.
-     * @param float $cost The total cost.
-     * @return void
-     * @throws \InvalidArgumentException If percentage exceeds 100%.
-     */
-    private function validateCompletedPercentage(float $completedPercent, float $completedAmount, float $cost): void
-    {
-        if ($completedPercent > 100) {
-            throw new \InvalidArgumentException("Completed percentage ({$completedPercent}%) cannot exceed 100%");
-        }
-
-        $expectedPercent = $cost > 0 ? ($completedAmount / $cost * 100) : 0;
-        $roundingTolerance = 1;
-        $difference = abs($completedPercent - $expectedPercent);
-
-        if ($difference > $roundingTolerance) {
-            throw new \InvalidArgumentException(
-                "Completed percentage doesn't match the expected value. Expected: {$expectedPercent}%, Actual: {$completedPercent}%"
-            );
-        }
-    }
 }
diff --git a/models/composite/oDrawManagement/DrawRequestsHistory.php b/models/composite/oDrawManagement/DrawRequestsHistory.php
index 0adc480cc4..7043868669 100644
--- a/models/composite/oDrawManagement/DrawRequestsHistory.php
+++ b/models/composite/oDrawManagement/DrawRequestsHistory.php
@@ -2,6 +2,7 @@
 namespace models\composite\oDrawManagement;

 use models\types\strongType;
+use models\composite\oDrawManagement\traits\PropertiesMapper;
 use models\lendingwise\tblDrawRequests_h;
 use models\lendingwise\tblDrawRequestLineItems_h;
 use models\lendingwise\db\tblDrawRequestLineItems_h_db;
@@ -10,6 +11,7 @@

 class DrawRequestsHistory extends strongType
 {
+    use PropertiesMapper;
     /**
      * @var int|null The ID of the history record.
      */
@@ -73,6 +75,7 @@
      */
     public function __construct(?tblDrawRequests_h $drawRequestHistory = null) {
         if ($drawRequestHistory == null) $drawRequestHistory = new tblDrawRequests_h();
+        $this->drawRequestHistory = $drawRequestHistory;
         $this->setProperties($drawRequestHistory);
         if ($drawRequestHistory->id) $this->loadLineItems();
     }
@@ -93,16 +96,8 @@
      * @return void
      */
     private function setProperties(tblDrawRequests_h $drawRequestHistory): void {
-        $this->drawRequestHistory = $drawRequestHistory;
-        $this->id = $drawRequestHistory->id;
-        $this->drawId = $drawRequestHistory->drawId;
-        $this->status = $drawRequestHistory->status;
-        $this->submittedAt = $drawRequestHistory->submittedAt;
-        $this->amountRequested = $drawRequestHistory->amountRequested;
-        $this->amountApproved = $drawRequestHistory->amountApproved;
-        $this->wireAmount = $drawRequestHistory->wireAmount;
-        $this->wireSentDate = $drawRequestHistory->wireSentDate;
-        $this->createdAt = $drawRequestHistory->createdAt;
+        $this->setPropertiesFromObject($drawRequestHistory);
+        $this->drawRequestHistory = $drawRequestHistory; // Ensure the DB object is explicitly set
     }

     /**
diff --git a/models/composite/oDrawManagement/traits/DrawRequestHistoryHandler.php b/models/composite/oDrawManagement/traits/DrawRequestHistoryHandler.php
new file mode 100644
index 0000000000..8a4e1d7a8d
--- /dev/null
+++ b/models/composite/oDrawManagement/traits/DrawRequestHistoryHandler.php
@@ -0,0 +1,63 @@
+<?php
+namespace models\composite\oDrawManagement\traits;
+
+use models\composite\oDrawManagement\DrawRequest;
+use models\composite\oDrawManagement\DrawRequestsHistory;
+
+/**
+ * Trait DrawRequestHistoryHandler
+ * Handles history creation and updates for DrawRequestManager.
+ */
+trait DrawRequestHistoryHandler
+{
+    /**
+     * Handle draw request history creation and updates.
+     * @param string $status The new status.
+     * @param array $lineItemsData The post data containing line items.
+     * @return void
+     */
+    private function handleDrawRequestHistory(string $status, array $lineItemsData): void
+    {
+        $previousStatus = $this->drawRequest->status;
+
+        if ($status === DrawRequest::STATUS_PENDING && $previousStatus !== DrawRequest::STATUS_REJECTED) {
+            DrawRequestsHistory::createHistoryRecord($this->drawRequest->id, $status, $lineItemsData);
+        } else {
+            $preparedLineItemsData = $this->prepareLineItemsDataForHistory($lineItemsData, $status);
+            DrawRequestsHistory::updateLatestHistoryRecord($this->drawRequest->id, $status, $preparedLineItemsData);
+        }
+    }
+
+    /**
+     * Prepare line items data for history creation.
+     * @param array $lineItemsData The raw line items data from post.
+     * @param string $status The current status of the request.
+     * @return array Prepared line items data with calculated values.
+     */
+    private function prepareLineItemsDataForHistory(array $lineItemsData, string $status): array
+    {
+        $preparedData = [];
+
+        foreach ($lineItemsData as $lineItemId => $lineItemData) {
+            $lineItem = $this->getLineItemById((int)$lineItemId);
+            if (!$lineItem) {
+                continue;
+            }
+
+            $preparedData[$lineItemId] = [
+                'completedAmount' => $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status),
+                'completedPercent' => $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status),
+                'requestedAmount' => $this->getLineItemRequestedAmount($lineItem, $lineItemData, $status),
+                'approvedAmount' => $this->getLineItemApprovedAmount($lineItemData, $status),
+                // For history, "disbursed" is the just-approved amount for this draw.
+                'disbursedAmount' => ($status === DrawRequest::STATUS_APPROVED) ? ($lineItemData['requestedAmount'] ?? 0) : 0,
+                'notes' => $lineItemData['notes'] ?? null,
+                'lenderNotes' => $lineItemData['lenderNotes'] ?? null
+            ];
+        }
+
+        return $preparedData;
+    }
+}
diff --git a/models/composite/oDrawManagement/traits/DrawRequestValidation.php b/models/composite/oDrawManagement/traits/DrawRequestValidation.php
new file mode 100644
index 0000000000..f764a88c03
--- /dev/null
+++ b/models/composite/oDrawManagement/traits/DrawRequestValidation.php
@@ -0,0 +1,118 @@
+<?php
+namespace models\composite\oDrawManagement\traits;
+
+use models\composite\oDrawManagement\BorrowerDrawLineItem;
+use models\composite\oDrawManagement\DrawRequest;
+
+/**
+ * Trait DrawRequestValidation
+ * Handles validation logic for DrawRequestManager.
+ */
+trait DrawRequestValidation
+{
+    /**
+     * Validate line item data for pending or approved status.
+     * @param BorrowerDrawLineItem $lineItem The line item object.
+     * @param array $lineItemData The line item data to validate.
+     * @param string $status The status of the draw request.
+     * @return void
+     * @throws \InvalidArgumentException If validation fails.
+     */
+    private function validateLineItemData(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): void
+    {
+        // Only validate for pending or approved status
+        if (!in_array($status, [DrawRequest::STATUS_PENDING, DrawRequest::STATUS_APPROVED])) {
+            return;
+        }
+
+        if (!$lineItem instanceof BorrowerDrawLineItem) {
+            throw new \InvalidArgumentException("Line item cannot be null for validation");
+        }
+
+        $cost = (float)($lineItemData['cost'] ?? $lineItem->cost);
+        $completedAmount = (float)$lineItem->completedAmount;
+        $requestedAmount = isset($lineItemData['requestedAmount']) ? (float)$lineItemData['requestedAmount'] : 0;
+
+        $newCompletedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
+        $newCompletedPercent = $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status);
+
+        // Rule 1: None of these can be negative
+        $this->validateNonNegativeValues($cost, $completedAmount, $requestedAmount, $newCompletedAmount);
+
+        // Rule 2: requestedAmount and disbursedAmount don't exceed cost or available amount
+        $this->validateAmountsNotExceedingCost($cost, $requestedAmount, $completedAmount);
+
+        // Rule 3: completedPercentage can't be more than 100%
+        $this->validateCompletedPercentage($newCompletedPercent, $newCompletedAmount, $cost);
+    }
+
+    /**
+     * Validate that all amounts are non-negative.
+     * @param float ...$amounts Variable number of amounts to validate.
+     * @return void
+     * @throws \InvalidArgumentException If any amount is negative.
+     */
+    private function validateNonNegativeValues(float ...$amounts): void
+    {
+        $fieldNames = ['cost', 'completedAmount', 'requestedAmount', 'newCompletedAmount'];
+
+        foreach ($amounts as $index => $amount) {
+            if ($amount < 0) {
+                $fieldName = $fieldNames[$index] ?? 'amount';
+                throw new \InvalidArgumentException("Line item {$fieldName} cannot be negative. Value: {$amount}");
+            }
+        }
+    }
+
+    /**
+     * Validate that requested amount doesn't exceed available budget.
+     * @param float $cost The total cost of the line item.
+     * @param float $requestedAmount The requested amount.
+     * @param float $completedAmount The currently completed amount.
+     * @return void
+     * @throws \InvalidArgumentException If amounts exceed cost.
+     */
+    private function validateAmountsNotExceedingCost(float $cost, float $requestedAmount, float $completedAmount): void
+    {
+        $availableAmount = $cost - $completedAmount;
+
+        // Add a small tolerance for floating point inaccuracies
+        if ($requestedAmount > $availableAmount + 0.001) {
+            throw new \InvalidArgumentException(
+                "Requested amount (" . number_format($requestedAmount, 2) . ") cannot exceed available amount (" . number_format($availableAmount, 2) . "). " .
+                "Cost: " . number_format($cost, 2) . ", Completed: " . number_format($completedAmount, 2)
+            );
+        }
+    }
+
+    /**
+     * Validate that completed percentage doesn't exceed 100% or is not calculated correctly.
+     * @param float $completedPercent The completed percentage.
+     * @param float $completedAmount The completed amount.
+     * @param float $cost The total cost.
+     * @return void
+     * @throws \InvalidArgumentException If percentage exceeds 100%.
+     */
+    private function validateCompletedPercentage(float $completedPercent, float $completedAmount, float $cost): void
+    {
+        // Add a small tolerance for floating point inaccuracies
+        if ($completedPercent > 100.01) {
+            throw new \InvalidArgumentException("Completed percentage ({$completedPercent}%) cannot exceed 100%");
+        }
+
+        if ($cost > 0) {
+            $expectedPercent = ($completedAmount / $cost) * 100;
+            // Allow for a small rounding difference
+            $difference = abs($completedPercent - $expectedPercent);
+            if ($difference > 1) { // Allow up to 1% difference
+                throw new \InvalidArgumentException(
+                    "Completed percentage ({$completedPercent}%) doesn't align with the completed amount. Expected around: " . round($expectedPercent, 2) . "%"
+                );
+            }
+        } elseif ($completedPercent != 0) {
+            throw new \InvalidArgumentException("Completed percentage should be 0 when cost is 0.");
+        }
+    }
+}
diff --git a/models/composite/oDrawManagement/traits/PropertiesMapper.php b/models/composite/oDrawManagement/traits/PropertiesMapper.php
new file mode 100644
index 0000000000..f98642a8a8
--- /dev/null
+++ b/models/composite/oDrawManagement/traits/PropertiesMapper.php
@@ -0,0 +1,22 @@
+<?php
+namespace models\composite\oDrawManagement\traits;
+
+/**
+ * Trait PropertiesMapper
+ * Automates the mapping of properties from a source object to the class using this trait.
+ */
+trait PropertiesMapper
+{
+    /**
+     * Sets class properties from a source object's public properties.
+     *
+     * @param object $sourceObject The object to get properties from.
+     * @return void
+     */
+    protected function setPropertiesFromObject(object $sourceObject): void
+    {
+        foreach (get_object_vars($sourceObject) as $property => $value) {
+            if (property_exists($this, $property)) {
+                $this->{$property} = $value;
+            }
+        }
+    }
+}
