$(document).ready(function() {
    function handleStatusChange() {
        const selectedValue = $('.statusAction').val();
        if (selectedValue === 'rejected' || currentStatus === 'rejected') {
            $('.col-reject-reason').removeClass('hide');
        } else {
            $('.col-reject-reason').addClass('hide');
        }
        validateSaveButton();
    }

    $('.statusAction').on('change', handleStatusChange);

    // Setup input handlers using shared utility
    DrawRequestUtils.setupInputHandlers(validateSaveButton);

    handleStatusChange();
    initializeLenderNotesModal();
    validateSaveButton();

    $saveBtn.on('click', function(e) {
        e.preventDefault();
        submitDrawManagementData();
    });

    // Handle Export Table button click
    $('#exportTableBtn').on('click', function(e) {
        e.preventDefault();
        exportTableToPdf();
    });
});

function validateSaveButton() {
    const selectedStatus = $('.statusAction').val();

    // check if at least one field has a non-zero value
    if (selectedStatus === statusApproved && sowApproved) {
        const hasNonZeroValue = DrawRequestUtils.hasNonZeroValues();
        $saveBtn.prop('disabled', !hasNonZeroValue);
    } else {
        $saveBtn.prop('disabled', false);
    }
}

// Common functions are now in drawRequest.js

function initializeLenderNotesModal() {
    const $buttons = $('.lender-note-btn');
    if ($buttons.length === 0) {
        console.warn('No lender note buttons found. Check if the table is loaded and buttons have the correct class.');
        return;
    }

    let currentLineItemId = null;
    let currentButton = null;

    $(document).on('click', '.lender-note-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);
        currentLineItemId = $btn.data('line-item-id');
        currentButton = $btn;
        const currentNote = $btn.find('i').data('original-title') || '';
        $('#lenderNotesTextarea').val(currentNote);
        $('#lenderNotesModal').modal('show');
    });

    $('#saveLenderNote').on('click', function() {
        const newNote = $('#lenderNotesTextarea').val().trim();

        if (currentButton) {
            const $icon = currentButton.find('i');
            $icon.data('original-title', newNote);
            $icon.attr('data-original-title', newNote);

            if (newNote) {
                $icon.removeClass('text-muted').addClass('text-primary');
            } else {
                $icon.removeClass('text-primary').addClass('text-muted');
            }
        }

        $('#lenderNotesModal').modal('hide');
    });

    $('#lenderNotesModal').on('hidden.bs.modal', function() {
        currentLineItemId = null;
        currentButton = null;
        $('#lenderNotesTextarea').val('');
    });
}

function submitDrawManagementData() {
    const status = $('#status').val();
    const lineItems = {};

    $('.line-item').each(function() {
        const $row = $(this);
        const lineItemId = $row.find('.lender-note-btn').data('line-item-id');

        if (lineItemId) {
            const borrowerNotes = $row.find('input[name="notes"]').val() || '';
            const lenderNotes = $row.find('.lender-note-btn > i').data('original-title') || '';
            const rejectReason = status === 'rejected' ? $row.find('select[name="rejectReason"]').val() : '';

            lineItems[lineItemId] = {
                notes: borrowerNotes,
                lenderNotes: lenderNotes,
                rejectReason: rejectReason
            };
            if($('.requested-amount').length) {
                const requestedAmount = parseFloat($row.find('.requested-amount').val()) || 0;
                lineItems[lineItemId].requestedAmount = requestedAmount;
            }

        }
    });

    const postData = {
        lmrid: lmrid,
        status: status,
        lineItems: lineItems
    };

    $.ajax({
        url: '/backoffice/api_v2/draw_management/LoanFile',
        type: 'POST',
        contentType: 'application/json',
        dataType: 'json',
        data: JSON.stringify(postData),
        beforeSend: function() {
            $saveBtn.prop('disabled', true).text('Saving...');
        },
        success: function(response) {
            if (response.success) {
                toastrNotification('Draw Request Status Updated!', 'success');
                setTimeout(function() {
                    location.reload();
                }, 1500);
            $saveBtn.prop('disabled', false).text('Save');
            } else {
                toastrNotification('Error: ' + (response.message || 'Failed to update Draw Request Status'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr, status, error);
            let errorMsg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'An error occurred while saving data. Please try again.';
            toastrNotification(errorMsg, 'error');
        }
    });
}

function exportTableToPdf() {
    const $exportBtn = $('#exportTableBtn');
    const originalText = $exportBtn.html();

    // Show loading state
    $exportBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');

    // Prepare data for API call - now we only need the LMR ID
    const postData = {
        lmrId: lmrid
    };

    $.ajax({
        url: '/backoffice/api_v2/draw_management/ExportPdf',
        type: 'POST',
        contentType: 'application/json',
        dataType: 'json',
        data: JSON.stringify(postData),
        success: function(response) {
            if (response.success) {
                // Convert base64 to blob and trigger download
                const byteCharacters = atob(response.pdf_data);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const blob = new Blob([byteArray], { type: 'application/pdf' });

                // Create download link
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                toastrNotification('PDF exported successfully!', 'success');
            } else {
                toastrNotification('Error: ' + (response.message || 'Failed to generate PDF'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('PDF Export Error:', xhr, status, error);
            let errorMsg = 'An error occurred while generating the PDF. Please try again.';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMsg = xhr.responseJSON.message;
            }
            toastrNotification(errorMsg, 'error');
        },
        complete: function() {
            // Restore button state
            $exportBtn.prop('disabled', false).html(originalText);
        }
    });
}
