<?php
namespace models\composite\oDrawManagement\traits;

use models\lendingwise\db\tblDrawRequestLineItems_db as tblLineItems;

/**
 * Trait PropertiesMapper
 *
 * Provides automated property mapping from database objects to class properties.
 * Reduces boilerplate code in setProperties methods across composite models.
 */
trait PropertiesMapper
{
    /**
     * Automatically map properties from a database object to class properties.
     * This method uses reflection to map properties with the same names.
     *
     * @param object $dbObject The database object to map from.
     * @param array $propertyMap Optional custom property mapping ['classProperty' => 'dbProperty'].
     * @param array $defaultValues Optional default values for properties ['property' => 'defaultValue'].
     * @param string|null $dbObjectProperty Optional property name to store the database object.
     * @return void
     */
    protected function mapPropertiesFromDbObject(
        object $dbObject,
        array $propertyMap = [],
        array $defaultValues = [],
        ?string $dbObjectProperty = null
    ): void {
        // Get all public properties of the current class
        $reflection = new \ReflectionClass($this);
        $classProperties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC);

        foreach ($classProperties as $property) {
            $propertyName = $property->getName();

            // Skip the database object property itself
            if ($dbObjectProperty && $propertyName === $dbObjectProperty) {
                continue;
            }

            // Determine the source property name
            $sourceProperty = $propertyMap[$propertyName] ?? $propertyName;

            // Map the property if it exists in the database object
            if (property_exists($dbObject, $sourceProperty)) {
                $value = $dbObject->$sourceProperty;

                // Apply default value if the database value is null and a default is provided
                if ($value === null && isset($defaultValues[$propertyName])) {
                    $value = $defaultValues[$propertyName];
                }

                $this->$propertyName = $value;
            } elseif (isset($defaultValues[$propertyName])) {
                // Set default value if property doesn't exist in database object
                $this->$propertyName = $defaultValues[$propertyName];
            }
        }

        // Store the database object if a property name is provided
        if ($dbObjectProperty && property_exists($this, $dbObjectProperty)) {
            $this->$dbObjectProperty = $dbObject;
        }
    }

    /**
     * Generic setProperties method that can be used by most composite models.
     *
     * @param object $dbObject The database object to map from.
     * @param array $customMapping Optional custom property mapping.
     * @param array $defaultValues Optional default values.
     * @return void
     */
    protected function setProperties(object $dbObject, array $customMapping = [], array $defaultValues = []): void
    {
        // Determine the database object property name based on class naming convention
        $className = (new \ReflectionClass($this))->getShortName();
        $dbObjectProperty = $this->getImplementingClassPropertyName($className);

        // Set default values for common properties
        $commonDefaults = [
            tblLineItems::COLUMN_DESCRIPTION => '',
            tblLineItems::COLUMN_NOTES => '',
            tblLineItems::COLUMN_LENDERNOTES => '',
            tblLineItems::COLUMN_REJECTREASON => '',
            tblLineItems::COLUMN_COMPLETEDAMOUNT => 0.00,
            tblLineItems::COLUMN_COMPLETEDPERCENT => 0.00,
            tblLineItems::COLUMN_REQUESTEDAMOUNT => 0.00,
            tblLineItems::COLUMN_DISBURSEDAMOUNT => 0.00,
        ];

        $mergedDefaults = array_merge($commonDefaults, $defaultValues);

        $this->mapPropertiesFromDbObject($dbObject, $customMapping, $mergedDefaults, $dbObjectProperty);
    }

    /**
     * Determine the database object property name based on class naming conventions.
     *
     * @param string $className The class name.
     * @return string The expected database object property name.
     */
    private function getImplementingClassPropertyName(string $className): string
    {
        // Map class names to their expected database object property names
        $classToPropertyMap = [
            'DrawRequest' => 'drawRequest',
            'BorrowerDrawCategory' => 'category',
            'BorrowerDrawLineItem' => 'lineItem',
            'DrawRequestsHistory' => 'drawRequestHistory',
            'SowCategory' => 'category',
            'SowLineItem' => 'lineItem',
            'SowTemplate' => 'template',
        ];

        return $classToPropertyMap[$className] ?? strtolower($className);
    }
}
