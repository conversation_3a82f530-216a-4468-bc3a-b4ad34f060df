<?php
namespace models\composite\oDrawManagement\traits;

use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\composite\oDrawManagement\DrawRequest;
use models\composite\oDrawManagement\DrawRequestsHistory;
use models\lendingwise\db\tblDrawRequestLineItems_h_db as tblLineItemsHistory;
use models\lendingwise\db\tblDrawRequests_h_db as tblRequestsHistory;

/**
 * Trait DrawRequestHistoryHandler
 *
 * Provides history handling methods for draw requests.
 * Extracted from DrawRequestManager to improve maintainability and adhere to Single Responsibility Principle.
 */
trait DrawRequestHistoryHandler
{
    /**
     * Handle draw request history creation and updates.
     * @param string $status The new status.
     * @param array $lineItemsData The line items data.
     * @return void
     */
    protected function handleDrawRequestHistory(string $status, array $lineItemsData): void
    {
        $previousStatus = $this->drawRequest->status;

        if ($status === DrawRequest::STATUS_PENDING && $previousStatus !== DrawRequest::STATUS_REJECTED) {
            DrawRequestsHistory::createHistoryRecord($this->drawRequest->id, $status, $lineItemsData);
        }
        else {
            $preparedLineItemsData = $this->prepareLineItemsDataForHistory($lineItemsData, $status);
            DrawRequestsHistory::updateLatestHistoryRecord($this->drawRequest->id, $status, $preparedLineItemsData);
        }
    }

    /**
     * Prepare line items data for history creation.
     * @param array $lineItemsData The raw line items data from post.
     * @param string $status The status of the draw request.
     * @return array Prepared line items data with calculated values.
     */
    protected function prepareLineItemsDataForHistory(array $lineItemsData, string $status): array
    {
        $preparedData = [];

        foreach ($lineItemsData as $lineItemId => $lineItemData) {
            $lineItem = $this->getLineItemById((int)$lineItemId);
            if (!$lineItem) {
                continue;
            }

            $preparedData[$lineItemId] = [
                tblLineItemsHistory::COLUMN_COMPLETEDAMOUNT => $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status),
                tblLineItemsHistory::COLUMN_COMPLETEDPERCENT => $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status),
                tblLineItemsHistory::COLUMN_REQUESTEDAMOUNT => $this->getLineItemRequestedAmount($lineItem, $lineItemData, $status),
                tblLineItemsHistory::COLUMN_DISBURSEDAMOUNT => $this->getLineItemDisbursedAmount($lineItem, $lineItemData, $status),
                tblRequestsHistory::COLUMN_AMOUNTAPPROVED => $this->getLineItemApprovedAmount($lineItemData, $status),
                'notes' => $lineItemData['notes'] ?? null,
                'lenderNotes' => $lineItemData['lenderNotes'] ?? null
            ];
        }

        return $preparedData;
    }

    /**
     * Abstract methods that must be implemented by classes using this trait.
     * These methods are required for the history handling logic to work properly.
     */
    abstract protected function getLineItemById(int $lineItemId): ?BorrowerDrawLineItem;
    abstract protected function getLineItemCompletedAmount(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
    abstract protected function getLineItemCompletedPercent(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
    abstract protected function getLineItemRequestedAmount(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
    abstract protected function getLineItemApprovedAmount(array $lineItemData, string $status): float;
    abstract protected function getLineItemDisbursedAmount(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
}
