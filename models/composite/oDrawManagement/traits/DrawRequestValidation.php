<?php
namespace models\composite\oDrawManagement\traits;

use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\composite\oDrawManagement\DrawRequest;

/**
 * Trait DrawRequestValidation
 *
 * Provides validation methods for draw request line items.
 * Extracted from DrawRequestManager to improve maintainability and adhere to Single Responsibility Principle.
 */
trait DrawRequestValidation
{
    /**
     * Validate line item data for pending or approved status.
     * @param BorrowerDrawLineItem $lineItem The line item object.
     * @param array $lineItemData The line item data to validate.
     * @param string $status The status of the draw request.
     * @return void
     * @throws \InvalidArgumentException If validation fails.
     */
    protected function validateLineItemData(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): void
    {
        // Only validate for pending or approved status
        if (!in_array($status, [DrawRequest::STATUS_PENDING, DrawRequest::STATUS_APPROVED])) {
            return;
        }

        if (!$lineItem instanceof BorrowerDrawLineItem) {
            throw new \InvalidArgumentException("Line item cannot be null for validation");
        }

        $cost = (float)($lineItemData['cost'] ?? $lineItem->cost);
        $completedAmount = (float)$lineItem->completedAmount;
        $requestedAmount = isset($lineItemData['requestedAmount']) ? (float)$lineItemData['requestedAmount'] : 0;
        $disbursedAmount = (float)$lineItem->disbursedAmount;

        $newCompletedAmount = $this->getLineItemCompletedAmount($lineItem, $lineItemData, $status);
        $newCompletedPercent = $this->getLineItemCompletedPercent($lineItem, $lineItemData, $status);
        $newDisbursedAmount = $status === DrawRequest::STATUS_APPROVED
            ? $disbursedAmount + $requestedAmount
            : $disbursedAmount;

        // Rule 1: None of these can be negative
        $this->validateNonNegativeValues($cost, $completedAmount, $requestedAmount, $disbursedAmount, $newCompletedAmount, $newDisbursedAmount);

        // Rule 2: requestedAmount and disbursedAmount don't exceed cost or available amount
        $this->validateAmountsNotExceedingCost($cost, $requestedAmount, $newDisbursedAmount, $newCompletedAmount);

        // Rule 3: completedPercentage can't be more than 100%
        $this->validateCompletedPercentage($newCompletedPercent, $newCompletedAmount, $cost);
    }

    /**
     * Validate that all amounts are non-negative.
     * @param float ...$amounts Variable number of amounts to validate.
     * @return void
     * @throws \InvalidArgumentException If any amount is negative.
     */
    protected function validateNonNegativeValues(float ...$amounts): void
    {
        $fieldNames = ['cost', 'completedAmount', 'requestedAmount', 'disbursedAmount', 'newCompletedAmount', 'newDisbursedAmount'];

        foreach ($amounts as $index => $amount) {
            if ($amount < 0) {
                $fieldName = $fieldNames[$index] ?? 'amount';
                throw new \InvalidArgumentException("Line item {$fieldName} cannot be negative. Value: {$amount}");
            }
        }
    }

    /**
     * Validate that requested and disbursed amounts don't exceed cost. Requested amount can't exceed cost minus completed amount.
     * @param float $cost The total cost of the line item.
     * @param float $requestedAmount The requested amount.
     * @param float $disbursedAmount The disbursed amount.
     * @param float $completedAmount The completed amount.
     * @return void
     * @throws \InvalidArgumentException If amounts exceed cost.
     */
    protected function validateAmountsNotExceedingCost(float $cost, float $requestedAmount, float $disbursedAmount, float $completedAmount): void
    {
        $availableAmount = $cost - $completedAmount;

        if ($requestedAmount > $availableAmount) {
            throw new \InvalidArgumentException(
                "Requested amount ({$requestedAmount}) cannot exceed available amount ({$availableAmount}). " .
                "Cost: {$cost}, Completed: {$completedAmount}"
            );
        }

        if ($disbursedAmount > $cost) {
            throw new \InvalidArgumentException("Disbursed amount ({$disbursedAmount}) cannot exceed line item cost ({$cost})");
        }
    }

    /**
     * Validate that completed percentage doesn't exceed 100% or is not calculated correctly.
     * @param float $completedPercent The completed percentage.
     * @param float $completedAmount The completed amount.
     * @param float $cost The total cost.
     * @return void
     * @throws \InvalidArgumentException If percentage exceeds 100%.
     */
    protected function validateCompletedPercentage(float $completedPercent, float $completedAmount, float $cost): void
    {
        if ($completedPercent > 100) {
            throw new \InvalidArgumentException("Completed percentage ({$completedPercent}%) cannot exceed 100%");
        }

        $expectedPercent = $cost > 0 ? ($completedAmount / $cost * 100) : 0;
        $roundingTolerance = 1;
        $difference = abs($completedPercent - $expectedPercent);

        if ($difference > $roundingTolerance) {
            throw new \InvalidArgumentException(
                "Completed percentage doesn't match the expected value. Expected: {$expectedPercent}%, Actual: {$completedPercent}%"
            );
        }
    }

    /**
     * Abstract methods that must be implemented by classes using this trait.
     * These methods are required for the validation logic to work properly.
     */
    abstract protected function getLineItemCompletedAmount(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
    abstract protected function getLineItemCompletedPercent(BorrowerDrawLineItem $lineItem, array $lineItemData, string $status): float;
}
