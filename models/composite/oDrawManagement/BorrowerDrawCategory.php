<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequestCategories;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\db\tblDrawRequestLineItems_db;
use models\composite\oDrawManagement\BorrowerDrawLineItem;

class BorrowerDrawCategory extends strongType
{
    use PropertiesMapper;

    /**
     * @var int|null The ID of the category.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the draw request this category belongs to.
     */
    public ?int $drawId = null;

    /**
     * @var string|null The name of the category.
     */
    public ?string $categoryName = null;

    /**
     * @var string|null The description of the category.
     */
    public ?string $description = null;

    /**
     * @var int The display order of the category.
     */
    public ?int $order = 1;

    /**
     * @var string|null The creation date.
     */
    public ?string $createdAt = null;

    /**
     * @var string|null The last update date.
     */
    public ?string $updatedAt = null;

    /**
     * @var tblDrawRequestCategories|null The database table object for the category.
     */
    public ?tblDrawRequestCategories $category = null;

    /**
     * @var array|null An array of line items associated with this category.
     */
    private ?array $lineItems = null;

    /**
     * BorrowerDrawCategory constructor.
     * @param tblDrawRequestCategories|null $category The database category object to initialize from.
     */
    public function __construct(?tblDrawRequestCategories $category = null) {
        if ($category == null) $category = new tblDrawRequestCategories();
        $this->setProperties($category);
    }

    /**
     * Saves the current category object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $categoryData): array {
        $this->setFromArray($categoryData);
        $saved = $this->category->save();
        $this->id = $this->category->id;
        return $saved;
    }

    /**
     * Sets the properties of the category from an associative array.
     * @param array $categoryData Associative array containing category data.
     * @return void
     */
    private function setFromArray(array $categoryData): void {
        $this->category->id = $categoryData['id'] ?? null;
        $this->category->drawId = $categoryData['drawId'];
        $this->category->categoryName = $categoryData['name'];
        $this->category->description = $categoryData['description'] ?? '';
        $this->category->order = $categoryData['order'] ?? 1;
        $this->setProperties($this->category);
    }

    /**
     * Delete the category from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->category instanceof tblDrawRequestCategories) {
            $this->category->delete();
        }
    }

    /**
     * Loads line items associated with this category from the database.
     * @return void
     */
    private function loadLineItems(): void {
        $lineItemsData = tblDrawRequestLineItems::GetAll(
            [tblDrawRequestLineItems_db::COLUMN_CATEGORYID => $this->id],
            [tblDrawRequestLineItems_db::COLUMN_CATEGORYID => 'ASC', tblDrawRequestLineItems_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($lineItemsData as $lineItemData) {
            $this->addLineItem(new BorrowerDrawLineItem($lineItemData));
        }
        $this->sortLineItems();
    }

    /**
     * Adds a BorrowerDrawLineItem object to the category's line items.
     * @param BorrowerDrawLineItem $lineItem The line item object to add.
     * @return void
     */
    private function addLineItem(BorrowerDrawLineItem $lineItem): void {
        $this->lineItems[$lineItem->id] = $lineItem;
    }

    /**
     * Sorts the line items by their order property.
     * @return void
     */
    private function sortLineItems(): void {
        uasort($this->lineItems, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all line items associated with this category.
     * @return BorrowerDrawLineItem[] An array of BorrowerDrawLineItem objects.
     */
    public function getAllLineItems(): array {
        if (is_null($this->lineItems)) {
            $this->loadLineItems();
        }
        return $this->lineItems ?? [];
    }

    /**
     * Retrieves a specific line item by its ID.
     * @param int $lineItemId The ID of the line item to retrieve.
     * @return BorrowerDrawLineItem|null The BorrowerDrawLineItem object if found, otherwise null.
     */
    public function getLineItemById($lineItemId): ?BorrowerDrawLineItem {
        if (is_null($this->lineItems)) {
            $this->loadLineItems();
        }
        return $this->lineItems[$lineItemId] ?? null;
    }

    /**
     * Converts the category object and its line items to an associative array.
     * @return array An associative array representation of the category.
     */
    public function toArray(): array {
        $data = [
            "id" => $this->id,
            "drawId" => $this->drawId,
            "name" => $this->categoryName,
            "description" => $this->description,
            "order" => $this->order,
            "lineItems" => []
        ];

        $lineItems = $this->getAllLineItems();
        foreach ($lineItems as $lineItem) {
            $data["lineItems"][] = $lineItem->toArray();
        }

        return $data;
    }

    /**
     * Sets class properties from a tblDrawRequestCategories object
     *
     * @param tblDrawRequestCategories $category
     * @return void
     */
    private function setProperties(tblDrawRequestCategories $category): void {
        $this->id = $category->id;
        $this->drawId = $category->drawId;
        $this->categoryName = $category->categoryName;
        $this->description = $category->description;
        $this->order = $category->order;
        $this->createdAt = $category->createdAt;
        $this->updatedAt = $category->updatedAt;
        $this->category = $category;
    }
}
