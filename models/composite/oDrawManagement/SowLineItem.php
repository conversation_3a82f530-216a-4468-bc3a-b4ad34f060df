<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawTemplateLineItems;

class SowLineItem extends strongType
{
    use PropertiesMapper;

    /**
     * @var int|null The ID of the line item.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the template this line item belongs to.
     */
    public ?int $templateId = null;

    /**
     * @var int|null The ID of the category this line item belongs to.
     */
    public ?int $categoryId = null;

    /**
     * @var string|null The name of the line item.
     */
    public ?string $name = null;

    /**
     * @var string|null The description of the line item.
     */
    public ?string $description = null;

    /**
     * @var int The display order of the line item.
     */
    public ?int $order = 1;

    /**
     * @var tblDrawTemplateLineItems|null The database table object for the line item.
     */
    public ?tblDrawTemplateLineItems $lineItem = null;

    /**
     * SowLineItem constructor.
     * @param tblDrawTemplateLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawTemplateLineItems $lineItem = null) {
        if ($lineItem == null) $lineItem = new tblDrawTemplateLineItems();
        $this->setProperties($lineItem);
    }

    /**
     * Saves the current line item object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItem->save();
        $this->id = $this->lineItem->id;
        return $saved;
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->lineItem->id = $lineItemData['id'] ?? null;
        $this->lineItem->templateId = $lineItemData['templateId'];
        $this->lineItem->categoryId = $lineItemData['categoryId'];
        $this->lineItem->name = $lineItemData['name'];
        $this->lineItem->description = $lineItemData['description'] ?? '';
        $this->lineItem->order = $lineItemData['order'] ?? 1;
        $this->setProperties($this->lineItem);
    }

    /**
     * Delete the lineitem from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->lineItem instanceof tblDrawTemplateLineItems) {
            $this->lineItem->delete();
        }
    }

    /**
     * Converts the line item object to an associative array.
     * @return array An associative array representation of the line item.
     */
    public function toArray(): array {
        return [
            "id" => $this->id,
            "templateId" => $this->templateId,
            "categoryId" => $this->categoryId,
            "name" => $this->name,
            "description" => $this->description,
            "order" => $this->order
        ];
    }

}
