<?php
namespace models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowLineItem;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawTemplateLineItems;
use models\lendingwise\db\tblDrawTemplateLineItems_db;
use models\lendingwise\tblDrawTemplateCategories;
use models\types\strongType;

class SowCategory extends strongType
{
    use PropertiesMapper;

    /**
     * @var int|null The ID of the category.
     */
    public ?int $id = null;

    /**
     * @var int|null The ID of the template this category belongs to.
     */
    public ?int $templateId = null;

    /**
     * @var string The name of the category.
     */
    public ?string $name = '';

    /**
     * @var string The description of the category.
     */
    public ?string $description = '';

    /**
     * @var int The display order of the category.
     */
    public ?int $order = 0;

    /**
     * @var SowLineItem[] An array of line items associated with this category, indexed by line item ID.
     */
    public ?array $lineItems = [];

    /**
     * @var tblDrawTemplateCategories|null The database table object for the category.
     */
    public ?tblDrawTemplateCategories $category = null;

    /**
     * SowCategory constructor.
     * @param tblDrawTemplateCategories|null $category The database category object to initialize from.
     */
    public function __construct(?tblDrawTemplateCategories $category = null) {
        if ($category === null) $category = new tblDrawTemplateCategories();
        $this->setProperties($category);
        $this->loadLineItems();
    }

    /**
     * Loads line items associated with this category from the database.
     * @return void
     */
    private function loadLineItems(): void {
        $lineItemsData = tblDrawTemplateLineItems::GetAll(
            [tblDrawTemplateLineItems_db::COLUMN_CATEGORYID => $this->id],
            [tblDrawTemplateLineItems_db::COLUMN_CATEGORYID => 'ASC', tblDrawTemplateLineItems_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($lineItemsData as $lineItemData) {
            $this->addLineItem(new SowLineItem($lineItemData));
        }
        $this->sortLineItems();
    }

    /**
     * Saves the current category object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $categoryData): array {
        $this->setFromArray($categoryData);
        $saved = $this->category->save();
        $this->id = $this->category->id;
        return $saved;
    }

    /**
     * Delete the category from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->category instanceof tblDrawTemplateCategories) {
            $this->category->delete();
        }
    }

    /**
     * Sets the properties of the category from an associative array.
     * @param array $categoryData Associative array containing category data.
     * @return void
     */
    private function setFromArray(array $categoryData): void {
        $this->category->id = $categoryData['id'];
        $this->category->templateId = $categoryData['templateId'];
        $this->category->categoryName = $categoryData['name'];
        $this->category->description = $categoryData['description'] ?? '';
        $this->category->order = $categoryData['order'];

        $this->setProperties($this->category);
    }

    /**
     * Adds a SowLineItem object to the category's line items.
     * @param SowLineItem $lineItem The line item object to add.
     * @return void
     */
    private function addLineItem(SowLineItem $lineItem): void {
        $this->lineItems[$lineItem->id] = $lineItem;
    }

    /**
     * Sorts the line items by their order property.
     * @return void
     */
    private function sortLineItems(): void {
        uasort($this->lineItems, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all line items associated with this category.
     * @return SowLineItem[] An array of SowLineItem objects.
     */
    public function getAllLineItems(): array {
        return $this->lineItems;
    }

    /**
     * Retrieves a specific line item by its ID.
     * @param int $lineItemId The ID of the line item to retrieve.
     * @return SowLineItem|null The SowLineItem object if found, otherwise null.
     */
    public function getLineItemById($lineItemId): ?SowLineItem {
        return $this->lineItems[$lineItemId] ?? null;
    }

    /**
     * Converts the category object and its line items to an associative array.
     * @return array An associative array representation of the category.
     */
    public function toArray(): array {
        $data = [
            "id" => $this->id,
            "templateId" => $this->templateId,
            "name" => $this->name,
            "description" => $this->description,
            "order" => $this->order,
            "lineItems" => []
        ];
        foreach ($this->lineItems as $lineItem) {
            $data["lineItems"][] = $lineItem->toArray();
        }

        return $data;
    }
}
